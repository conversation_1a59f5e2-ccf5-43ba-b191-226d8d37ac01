import type { AxiosResponse, AxiosError } from "axios";
import { apiClient } from "../config";
import type {
  GenerateIntegrationTokenRequest,
  GenerateIntegrationTokenResponse,
  ValidateIframeTokenResponse,
  TokenPaymentRequest,
  TokenPaymentResponse,
} from "../types/payment";

export const generateIntegrationToken = async (tokenData: GenerateIntegrationTokenRequest): Promise<GenerateIntegrationTokenResponse> => {
  const response: AxiosResponse<GenerateIntegrationTokenResponse> = await apiClient.post("/payments/generate-integration-token", tokenData);
  return response.data;
};

export const validateIframeToken = async (token: string, paymentMethod?: "card" | "google_pay"): Promise<ValidateIframeTokenResponse> => {
  const response: AxiosResponse<ValidateIframeTokenResponse> = await apiClient.post("/payments/validate-iframe-token", {
    token,
    paymentMethod,
  });
  return response.data;
};

export const processTokenPayment = async (paymentData: TokenPaymentRequest): Promise<TokenPaymentResponse> => {
  try {
    const response: AxiosResponse<TokenPaymentResponse> = await apiClient.post("/payments/process-token-payment", paymentData);
    return response.data;
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.data) {
      return axiosError.response.data as TokenPaymentResponse;
    }
    return {
      success: false,
      message: "Network error occurred during token payment processing",
      error: axiosError.message,
    };
  }
};
